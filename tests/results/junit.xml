<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="CLI Arguments" tests="9" assertions="14" errors="0" failures="0" skipped="0" time="0.937809">
    <testsuite name="Tests\Unit\CQRS\CommandBusTest" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php" tests="4" assertions="7" errors="0" failures="0" skipped="0" time="0.648862">
      <testcase name="it_can_dispatch_command" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php" line="50" class="Tests\Unit\CQRS\CommandBusTest" classname="Tests.Unit.CQRS.CommandBusTest" assertions="1" time="0.495928"/>
      <testcase name="it_throws_exception_for_unregistered_command" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php" line="71" class="Tests\Unit\CQRS\CommandBusTest" classname="Tests.Unit.CQRS.CommandBusTest" assertions="2" time="0.058522"/>
      <testcase name="it_can_register_command_handler" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php" line="34" class="Tests\Unit\CQRS\CommandBusTest" classname="Tests.Unit.CQRS.CommandBusTest" assertions="2" time="0.046854"/>
      <testcase name="it_can_add_middleware" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php" line="85" class="Tests\Unit\CQRS\CommandBusTest" classname="Tests.Unit.CQRS.CommandBusTest" assertions="2" time="0.047558"/>
    </testsuite>
    <testsuite name="Tests\Unit\CQRS\QueryBusTest" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php" tests="5" assertions="7" errors="0" failures="0" skipped="0" time="0.288947">
      <testcase name="it_can_register_query_handler" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php" line="35" class="Tests\Unit\CQRS\QueryBusTest" classname="Tests.Unit.CQRS.QueryBusTest" assertions="2" time="0.050084"/>
      <testcase name="it_can_dispatch_query" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php" line="51" class="Tests\Unit\CQRS\QueryBusTest" classname="Tests.Unit.CQRS.QueryBusTest" assertions="1" time="0.048025"/>
      <testcase name="it_throws_exception_for_unregistered_query" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php" line="74" class="Tests\Unit\CQRS\QueryBusTest" classname="Tests.Unit.CQRS.QueryBusTest" assertions="2" time="0.047050"/>
      <testcase name="it_can_cache_query_results" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php" line="88" class="Tests\Unit\CQRS\QueryBusTest" classname="Tests.Unit.CQRS.QueryBusTest" assertions="1" time="0.064525"/>
      <testcase name="it_returns_cached_result_when_available" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php" line="120" class="Tests\Unit\CQRS\QueryBusTest" classname="Tests.Unit.CQRS.QueryBusTest" assertions="1" time="0.079262"/>
    </testsuite>
  </testsuite>
</testsuites>
