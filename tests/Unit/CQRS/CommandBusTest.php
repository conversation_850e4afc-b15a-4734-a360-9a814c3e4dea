<?php

namespace Tests\Unit\CQRS;

use Tests\TestCase;
use App\Infrastructure\CQRS\CommandBus;
use App\Core\CQRS\Contracts\CommandInterface;
use App\Core\CQRS\Contracts\CommandHandlerInterface;
use App\Core\CQRS\Base\BaseCommand;
use App\Core\CQRS\Base\BaseCommandHandler;
use Illuminate\Container\Container;
use Mockery;

class CommandBusTest extends TestCase
{
    private CommandBus $commandBus;
    private Container $container;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->container = new Container();
        $this->commandBus = new CommandBus($this->container);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_register_command_handler(): void
    {
        // Arrange
        $commandClass = TestCommand::class;
        $handlerClass = TestCommandHandler::class;

        // Act
        $this->commandBus->registerHandler($commandClass, $handlerClass);

        // Assert
        $handlers = $this->commandBus->getRegisteredHandlers();
        $this->assertArrayHasKey($commandClass, $handlers);
        $this->assertEquals($handlerClass, $handlers[$commandClass]);
    }

    /** @test */
    public function it_can_dispatch_command(): void
    {
        // Arrange
        $command = new TestCommand('test data');
        $handler = Mockery::mock(TestCommandHandler::class);
        $handler->shouldReceive('handle')
            ->once()
            ->with($command)
            ->andReturn('success');

        $this->container->instance(TestCommandHandler::class, $handler);
        $this->commandBus->registerHandler(TestCommand::class, TestCommandHandler::class);

        // Act
        $result = $this->commandBus->dispatch($command);

        // Assert
        $this->assertEquals('success', $result);
    }

    /** @test */
    public function it_throws_exception_for_unregistered_command(): void
    {
        // Arrange
        $command = new TestCommand('test data');

        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('No handler registered for command: ' . TestCommand::class);

        // Act
        $this->commandBus->dispatch($command);
    }

    /** @test */
    public function it_can_add_middleware(): void
    {
        // Arrange
        $middlewareExecuted = false;
        $middleware = function ($command, $next) use (&$middlewareExecuted) {
            $middlewareExecuted = true;
            return $next($command);
        };

        $command = new TestCommand('test data');
        $handler = Mockery::mock(TestCommandHandler::class);
        $handler->shouldReceive('handle')
            ->once()
            ->with($command)
            ->andReturn('success');

        $this->container->instance(TestCommandHandler::class, $handler);
        $this->commandBus->registerHandler(TestCommand::class, TestCommandHandler::class);

        // Act
        $this->commandBus->addMiddleware($middleware);
        $result = $this->commandBus->dispatch($command);

        // Assert
        $this->assertTrue($middlewareExecuted);
        $this->assertEquals('success', $result);
    }
}

// Test sınıfları
class TestCommand extends BaseCommand
{
    public function __construct(
        public readonly string $data
    ) {
        parent::__construct();
    }

    public function rules(): array
    {
        return [
            'data' => 'required|string'
        ];
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'data' => $this->data
        ]);
    }
}

class TestCommandHandler extends BaseCommandHandler
{
    public function getCommandClass(): string
    {
        return TestCommand::class;
    }

    protected function execute(CommandInterface $command)
    {
        return 'success';
    }
}
