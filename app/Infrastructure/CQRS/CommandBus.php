<?php

namespace App\Infrastructure\CQRS;

use App\Core\CQRS\Contracts\CommandBusInterface;
use App\Core\CQRS\Contracts\CommandInterface;
use App\Core\CQRS\Contracts\CommandHandlerInterface;
use Illuminate\Container\Container;
use Illuminate\Support\Facades\Log;

/**
 * Command Bus Implementation
 * Command'ları ilgili handler'lara y<PERSON>
 */
class CommandBus implements CommandBusInterface
{
    protected Container $container;
    protected array $handlers = [];
    protected array $middlewares = [];

    public function __construct(Container $container)
    {
        $this->container = $container;
    }

    /**
     * Command'ı dispatch et
     *
     * @param CommandInterface $command
     * @return mixed
     * @throws \Exception
     */
    public function dispatch(CommandInterface $command)
    {
        $commandClass = get_class($command);
        
        if (!isset($this->handlers[$commandClass])) {
            throw new \InvalidArgumentException("No handler registered for command: {$commandClass}");
        }

        $handlerClass = $this->handlers[$commandClass];
        $handler = $this->container->make($handlerClass);

        if (!$handler instanceof CommandHandlerInterface) {
            throw new \InvalidArgumentException("Handler {$handlerClass} must implement CommandHandlerInterface");
        }

        // Middleware'leri çalıştır
        $pipeline = array_reduce(
            array_reverse($this->middlewares),
            function ($next, $middleware) {
                return function ($command) use ($middleware, $next) {
                    return $middleware($command, $next);
                };
            },
            function ($command) use ($handler) {
                return $handler->handle($command);
            }
        );

        $startTime = microtime(true);
        
        try {
            $result = $pipeline($command);
            
            $this->logCommandSuccess($command, $handler, microtime(true) - $startTime);
            
            return $result;
        } catch (\Exception $e) {
            $this->logCommandFailure($command, $handler, $e, microtime(true) - $startTime);
            throw $e;
        }
    }

    /**
     * Command handler'ı kaydet
     *
     * @param string $commandClass
     * @param string $handlerClass
     * @return void
     */
    public function registerHandler(string $commandClass, string $handlerClass): void
    {
        $this->handlers[$commandClass] = $handlerClass;
        
        Log::debug("Command handler registered", [
            'command' => $commandClass,
            'handler' => $handlerClass
        ]);
    }

    /**
     * Middleware ekle
     *
     * @param callable $middleware
     * @return void
     */
    public function addMiddleware(callable $middleware): void
    {
        $this->middlewares[] = $middleware;
    }

    /**
     * Kayıtlı handler'ları al
     *
     * @return array
     */
    public function getRegisteredHandlers(): array
    {
        return $this->handlers;
    }

    /**
     * Command başarısını logla
     *
     * @param CommandInterface $command
     * @param CommandHandlerInterface $handler
     * @param float $executionTime
     * @return void
     */
    protected function logCommandSuccess(
        CommandInterface $command, 
        CommandHandlerInterface $handler, 
        float $executionTime
    ): void {
        Log::info("Command dispatched successfully", [
            'command_id' => $command->getId(),
            'command_class' => get_class($command),
            'handler_class' => get_class($handler),
            'execution_time_ms' => round($executionTime * 1000, 2),
        ]);
    }

    /**
     * Command hatasını logla
     *
     * @param CommandInterface $command
     * @param CommandHandlerInterface $handler
     * @param \Exception $exception
     * @param float $executionTime
     * @return void
     */
    protected function logCommandFailure(
        CommandInterface $command, 
        CommandHandlerInterface $handler, 
        \Exception $exception,
        float $executionTime
    ): void {
        Log::error("Command dispatch failed", [
            'command_id' => $command->getId(),
            'command_class' => get_class($command),
            'handler_class' => get_class($handler),
            'execution_time_ms' => round($executionTime * 1000, 2),
            'error_message' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
        ]);
    }
}
