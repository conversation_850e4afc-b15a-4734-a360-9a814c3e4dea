<?php

namespace App\Infrastructure\CQRS;

use Illuminate\Support\ServiceProvider;
use Illuminate\Foundation\Application;
use App\Core\CQRS\Contracts\CommandBusInterface;
use App\Core\CQRS\Contracts\QueryBusInterface;
use App\Infrastructure\CQRS\CommandBus;
use App\Infrastructure\CQRS\QueryBus;
use App\Infrastructure\CQRS\Middleware\ValidationMiddleware;
use App\Infrastructure\CQRS\Middleware\LoggingMiddleware;
use App\Infrastructure\CQRS\Middleware\PerformanceMiddleware;

/**
 * CQRS Service Provider
 * CQRS infrastructure'ını Laravel container'a kaydeder
 */
class CQRSServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerCommandBus();
        $this->registerQueryBus();
        $this->registerMiddlewares();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->registerCommandHandlers();
        $this->registerQueryHandlers();
        $this->setupMiddlewares();
        $this->publishConfig();
    }

    /**
     * Command Bus'ı kaydet
     */
    protected function registerCommandBus(): void
    {
        $this->app->singleton(CommandBusInterface::class, function (Application $app) {
            return new CommandBus($app);
        });

        $this->app->alias(CommandBusInterface::class, 'command.bus');
    }

    /**
     * Query Bus'ı kaydet
     */
    protected function registerQueryBus(): void
    {
        $this->app->singleton(QueryBusInterface::class, function (Application $app) {
            return new QueryBus($app);
        });

        $this->app->alias(QueryBusInterface::class, 'query.bus');
    }

    /**
     * Middleware'leri kaydet
     */
    protected function registerMiddlewares(): void
    {
        $this->app->singleton(ValidationMiddleware::class);
        $this->app->singleton(LoggingMiddleware::class);
        $this->app->singleton(PerformanceMiddleware::class);
    }

    /**
     * Command handler'ları kaydet
     */
    protected function registerCommandHandlers(): void
    {
        $commandBus = $this->app->make(CommandBusInterface::class);
        
        // Config'den handler'ları al
        $handlers = config('cqrs.command_handlers', []);
        
        foreach ($handlers as $commandClass => $handlerClass) {
            $commandBus->registerHandler($commandClass, $handlerClass);
        }
    }

    /**
     * Query handler'ları kaydet
     */
    protected function registerQueryHandlers(): void
    {
        $queryBus = $this->app->make(QueryBusInterface::class);
        
        // Config'den handler'ları al
        $handlers = config('cqrs.query_handlers', []);
        
        foreach ($handlers as $queryClass => $handlerClass) {
            $queryBus->registerHandler($queryClass, $handlerClass);
        }
    }

    /**
     * Middleware'leri setup et
     */
    protected function setupMiddlewares(): void
    {
        $commandBus = $this->app->make(CommandBusInterface::class);
        $queryBus = $this->app->make(QueryBusInterface::class);

        // Command middleware'leri
        if (config('cqrs.middlewares.validation.enabled', true)) {
            $commandBus->addMiddleware($this->app->make(ValidationMiddleware::class));
        }

        if (config('cqrs.middlewares.logging.enabled', true)) {
            $commandBus->addMiddleware($this->app->make(LoggingMiddleware::class));
        }

        if (config('cqrs.middlewares.performance.enabled', true)) {
            $commandBus->addMiddleware($this->app->make(PerformanceMiddleware::class));
        }

        // Query middleware'leri
        if (config('cqrs.middlewares.validation.enabled', true)) {
            $queryBus->addMiddleware($this->app->make(ValidationMiddleware::class));
        }

        if (config('cqrs.middlewares.logging.enabled', true)) {
            $queryBus->addMiddleware($this->app->make(LoggingMiddleware::class));
        }

        if (config('cqrs.middlewares.performance.enabled', true)) {
            $queryBus->addMiddleware($this->app->make(PerformanceMiddleware::class));
        }
    }

    /**
     * Config dosyasını publish et
     */
    protected function publishConfig(): void
    {
        $this->publishes([
            __DIR__ . '/config/cqrs.php' => config_path('cqrs.php'),
        ], 'cqrs-config');
    }

    /**
     * Provides
     */
    public function provides(): array
    {
        return [
            CommandBusInterface::class,
            QueryBusInterface::class,
            'command.bus',
            'query.bus',
        ];
    }
}
