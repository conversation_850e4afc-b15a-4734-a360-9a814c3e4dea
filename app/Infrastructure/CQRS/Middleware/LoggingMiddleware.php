<?php

namespace App\Infrastructure\CQRS\Middleware;

use App\Core\CQRS\Contracts\CommandInterface;
use App\Core\CQRS\Contracts\QueryInterface;
use Illuminate\Support\Facades\Log;

/**
 * Logging Middleware
 * Command ve Query execution'larını loglar
 */
class LoggingMiddleware
{
    /**
     * Middleware'i çalıştır
     *
     * @param CommandInterface|QueryInterface $message
     * @param callable $next
     * @return mixed
     */
    public function __invoke($message, callable $next)
    {
        $startTime = microtime(true);
        $messageType = $this->getMessageType($message);
        
        $this->logMessageStart($message, $messageType);
        
        try {
            $result = $next($message);
            
            $this->logMessageSuccess($message, $messageType, microtime(true) - $startTime);
            
            return $result;
        } catch (\Exception $e) {
            $this->logMessageFailure($message, $messageType, $e, microtime(true) - $startTime);
            throw $e;
        }
    }

    /**
     * Message tipini belirle
     *
     * @param CommandInterface|QueryInterface $message
     * @return string
     */
    protected function getMessageType($message): string
    {
        if ($message instanceof CommandInterface) {
            return 'command';
        } elseif ($message instanceof QueryInterface) {
            return 'query';
        }
        
        return 'unknown';
    }

    /**
     * Message başlangıcını logla
     *
     * @param CommandInterface|QueryInterface $message
     * @param string $messageType
     * @return void
     */
    protected function logMessageStart($message, string $messageType): void
    {
        Log::info("CQRS {$messageType} started", [
            'message_id' => $message->getId(),
            'message_class' => get_class($message),
            'message_type' => $messageType,
            'created_at' => $message->getCreatedAt()->format('Y-m-d H:i:s'),
            'data' => $this->sanitizeData($message->toArray()),
        ]);
    }

    /**
     * Message başarısını logla
     *
     * @param CommandInterface|QueryInterface $message
     * @param string $messageType
     * @param float $executionTime
     * @return void
     */
    protected function logMessageSuccess($message, string $messageType, float $executionTime): void
    {
        Log::info("CQRS {$messageType} completed successfully", [
            'message_id' => $message->getId(),
            'message_class' => get_class($message),
            'message_type' => $messageType,
            'execution_time_ms' => round($executionTime * 1000, 2),
        ]);
    }

    /**
     * Message hatasını logla
     *
     * @param CommandInterface|QueryInterface $message
     * @param string $messageType
     * @param \Exception $exception
     * @param float $executionTime
     * @return void
     */
    protected function logMessageFailure($message, string $messageType, \Exception $exception, float $executionTime): void
    {
        Log::error("CQRS {$messageType} failed", [
            'message_id' => $message->getId(),
            'message_class' => get_class($message),
            'message_type' => $messageType,
            'execution_time_ms' => round($executionTime * 1000, 2),
            'error_message' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'error_file' => $exception->getFile(),
            'error_line' => $exception->getLine(),
        ]);
    }

    /**
     * Hassas verileri temizle
     *
     * @param array $data
     * @return array
     */
    protected function sanitizeData(array $data): array
    {
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'token',
            'api_key',
            'secret',
            'credit_card',
            'card_number',
            'cvv',
            'ssn',
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***REDACTED***';
            }
        }

        return $data;
    }
}
