<?php

namespace App\Infrastructure\CQRS\Middleware;

use App\Core\CQRS\Contracts\CommandInterface;
use App\Core\CQRS\Contracts\QueryInterface;
use Illuminate\Support\Facades\Log;

/**
 * Performance Middleware
 * Command ve Query performance'ını monitor eder
 */
class PerformanceMiddleware
{
    protected array $performanceThresholds;

    public function __construct()
    {
        $this->performanceThresholds = config('cqrs.performance.thresholds', [
            'command' => [
                'warning' => 1000, // 1 saniye
                'critical' => 5000, // 5 saniye
            ],
            'query' => [
                'warning' => 500,  // 0.5 saniye
                'critical' => 2000, // 2 saniye
            ],
        ]);
    }

    /**
     * Middleware'i çalıştır
     *
     * @param CommandInterface|QueryInterface $message
     * @param callable $next
     * @return mixed
     */
    public function __invoke($message, callable $next)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        $messageType = $this->getMessageType($message);
        
        try {
            $result = $next($message);
            
            $this->analyzePerformance(
                $message,
                $messageType,
                microtime(true) - $startTime,
                memory_get_usage(true) - $startMemory
            );
            
            return $result;
        } catch (\Exception $e) {
            $this->analyzePerformance(
                $message,
                $messageType,
                microtime(true) - $startTime,
                memory_get_usage(true) - $startMemory,
                $e
            );
            
            throw $e;
        }
    }

    /**
     * Message tipini belirle
     *
     * @param CommandInterface|QueryInterface $message
     * @return string
     */
    protected function getMessageType($message): string
    {
        if ($message instanceof CommandInterface) {
            return 'command';
        } elseif ($message instanceof QueryInterface) {
            return 'query';
        }
        
        return 'unknown';
    }

    /**
     * Performance'ı analiz et
     *
     * @param CommandInterface|QueryInterface $message
     * @param string $messageType
     * @param float $executionTime
     * @param int $memoryUsage
     * @param \Exception|null $exception
     * @return void
     */
    protected function analyzePerformance(
        $message,
        string $messageType,
        float $executionTime,
        int $memoryUsage,
        ?\Exception $exception = null
    ): void {
        $executionTimeMs = round($executionTime * 1000, 2);
        $memoryUsageMb = round($memoryUsage / 1024 / 1024, 2);
        
        $performanceData = [
            'message_id' => $message->getId(),
            'message_class' => get_class($message),
            'message_type' => $messageType,
            'execution_time_ms' => $executionTimeMs,
            'memory_usage_mb' => $memoryUsageMb,
            'status' => $exception ? 'failed' : 'success',
        ];

        // Performance threshold kontrolü
        $thresholds = $this->performanceThresholds[$messageType] ?? [];
        
        if (isset($thresholds['critical']) && $executionTimeMs > $thresholds['critical']) {
            Log::critical("CQRS {$messageType} performance critical", $performanceData);
        } elseif (isset($thresholds['warning']) && $executionTimeMs > $thresholds['warning']) {
            Log::warning("CQRS {$messageType} performance warning", $performanceData);
        } else {
            Log::debug("CQRS {$messageType} performance", $performanceData);
        }

        // Memory usage kontrolü
        if ($memoryUsageMb > 50) { // 50MB üzeri
            Log::warning("CQRS {$messageType} high memory usage", $performanceData);
        }

        // Metrics'e gönder (opsiyonel)
        $this->sendMetrics($messageType, $executionTimeMs, $memoryUsageMb, $exception !== null);
    }

    /**
     * Metrics'e performance verilerini gönder
     *
     * @param string $messageType
     * @param float $executionTimeMs
     * @param float $memoryUsageMb
     * @param bool $failed
     * @return void
     */
    protected function sendMetrics(string $messageType, float $executionTimeMs, float $memoryUsageMb, bool $failed): void
    {
        // Bu method'da metrics service'e veri gönderilebilir
        // Örneğin: Prometheus, StatsD, CloudWatch, vb.
        
        if (config('cqrs.performance.metrics.enabled', false)) {
            // Metrics implementation buraya gelecek
            Log::debug("Metrics sent", [
                'type' => $messageType,
                'execution_time_ms' => $executionTimeMs,
                'memory_usage_mb' => $memoryUsageMb,
                'failed' => $failed,
            ]);
        }
    }
}
