<?php

namespace App\Http\Controllers\Api\V1\Products;

use App\Http\Controllers\Controller;
use App\Application\Products\Commands\CreateProductCommand;
use App\Application\Products\Queries\GetProductsQuery;
use App\Application\Products\Queries\GetProductQuery;
use App\Core\CQRS\Contracts\CommandBusInterface;
use App\Core\CQRS\Contracts\QueryBusInterface;
use App\Core\Infrastructure\Api\Response\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * CQRS Product Controller
 * CQRS pattern kullanarak ürün işlemlerini yöneten controller
 */
class CQRSProductController extends Controller
{
    public function __construct(
        private CommandBusInterface $commandBus,
        private QueryBusInterface $queryBus
    ) {}

    /**
     * Ürün listesi getir
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = new GetProductsQuery(
                categoryId: $request->integer('category_id'),
                status: $request->boolean('status'),
                isFeatured: $request->boolean('is_featured'),
                isOnSale: $request->boolean('is_on_sale'),
                inStock: $request->boolean('in_stock'),
                minPrice: $request->float('min_price'),
                maxPrice: $request->float('max_price'),
                search: $request->string('search')->toString(),
                sortBy: $request->string('sort_by', 'created_at')->toString(),
                sortDirection: $request->string('sort_direction', 'desc')->toString(),
                limit: $request->integer('limit', 10),
                offset: $request->integer('offset', 0),
                includeVariants: $request->boolean('include_variants', false),
                includeAttributes: $request->boolean('include_attributes', false),
                includeImages: $request->boolean('include_images', false),
                attributes: $request->array('attributes', [])
            );

            $result = $this->queryBus->dispatch($query);

            return ApiResponse::success($result, 'Products retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Failed to get products', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return ApiResponse::error('Failed to retrieve products', 500);
        }
    }

    /**
     * Tek ürün getir
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $query = new GetProductQuery(
                id: $id,
                includeVariants: $request->boolean('include_variants', false),
                includeAttributes: $request->boolean('include_attributes', false),
                includeImages: $request->boolean('include_images', false)
            );

            $result = $this->queryBus->dispatch($query);

            return ApiResponse::success($result, 'Product retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Failed to get product', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);

            return ApiResponse::error('Product not found', 404);
        }
    }

    /**
     * Yeni ürün oluştur
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $command = new CreateProductCommand(
                name: $request->string('name')->toString(),
                slug: $request->string('slug')->toString(),
                sku: $request->string('sku')->toString(),
                price: $request->float('price'),
                stockQuantity: $request->integer('stock_quantity'),
                categoryId: $request->integer('category_id'),
                description: $request->string('description')->toString(),
                currency: $request->string('currency', 'TRY')->toString(),
                status: $request->boolean('status', true),
                isFeatured: $request->boolean('is_featured', false),
                salePrice: $request->float('sale_price'),
                saleStartsAt: $request->string('sale_starts_at')->toString(),
                saleEndsAt: $request->string('sale_ends_at')->toString(),
                weight: $request->float('weight'),
                weightUnit: $request->string('weight_unit', 'g')->toString(),
                length: $request->float('length'),
                width: $request->float('width'),
                height: $request->float('height'),
                dimensionUnit: $request->string('dimension_unit', 'cm')->toString(),
                metaTitle: $request->string('meta_title')->toString(),
                metaDescription: $request->string('meta_description')->toString(),
                metaKeywords: $request->string('meta_keywords')->toString(),
                images: $request->array('images', []),
                attributes: $request->array('attributes', []),
                lowStockThreshold: $request->integer('low_stock_threshold', 5),
                trackQuantity: $request->boolean('track_quantity', true),
                allowBackorders: $request->boolean('allow_backorders', false)
            );

            $result = $this->commandBus->dispatch($command);

            return ApiResponse::success($result, 'Product created successfully', 201);

        } catch (\InvalidArgumentException $e) {
            return ApiResponse::error($e->getMessage(), 422);
        } catch (\Exception $e) {
            Log::error('Failed to create product', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return ApiResponse::error('Failed to create product', 500);
        }
    }

    /**
     * CQRS Bus durumlarını kontrol et
     *
     * @return JsonResponse
     */
    public function status(): JsonResponse
    {
        try {
            $commandHandlers = $this->commandBus->getRegisteredHandlers();
            $queryHandlers = $this->queryBus->getRegisteredHandlers();

            return ApiResponse::success([
                'cqrs_status' => 'active',
                'command_handlers' => count($commandHandlers),
                'query_handlers' => count($queryHandlers),
                'registered_commands' => array_keys($commandHandlers),
                'registered_queries' => array_keys($queryHandlers),
            ], 'CQRS status retrieved successfully');

        } catch (\Exception $e) {
            return ApiResponse::error('Failed to get CQRS status', 500);
        }
    }
}
