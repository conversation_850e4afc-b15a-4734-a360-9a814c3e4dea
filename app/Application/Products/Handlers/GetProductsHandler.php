<?php

namespace App\Application\Products\Handlers;

use App\Application\Products\Queries\GetProductsQuery;
use App\Application\Products\DTOs\ProductDTO;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use App\Core\CQRS\Base\BaseQueryHandler;
use App\Core\CQRS\Contracts\QueryInterface;
use Illuminate\Support\Facades\Log;

/**
 * GetProductsHandler
 * Ürün listesi getirme query handler'ı
 */
class GetProductsHandler extends BaseQueryHandler
{
    public function __construct(
        private ProductRepositoryInterface $productRepository
    ) {}

    /**
     * Handler'ın desteklediği query sınıfı
     *
     * @return string
     */
    public function getQueryClass(): string
    {
        return GetProductsQuery::class;
    }

    /**
     * Query sonucunun cache'lenip cache'lenmeyeceği
     *
     * @return bool
     */
    public function isCacheable(): bool
    {
        return true;
    }

    /**
     * Query'yi gerçekten işleyen method
     *
     * @param QueryInterface $query
     * @return array
     */
    protected function execute(QueryInterface $query): array
    {
        /** @var GetProductsQuery $query */
        try {
            // Kriterlere göre ürünleri al
            $products = $this->productRepository->search(
                criteria: $query->getCriteria(),
                limit: $query->getLimit(),
                offset: $query->getOffset()
            );

            // Toplam sayıyı al
            $total = $this->productRepository->count($query->getCriteria());

            // DTO'lara dönüştür
            $productDTOs = array_map(
                fn($product) => ProductDTO::fromEntity($product),
                $products
            );

            return [
                'data' => $productDTOs,
                'total' => $total,
                'limit' => $query->getLimit(),
                'offset' => $query->getOffset(),
                'has_more' => ($query->getOffset() + $query->getLimit()) < $total,
                'filters' => [
                    'category_id' => $query->getCategoryId(),
                    'status' => $query->getStatus(),
                    'is_featured' => $query->isFeatured(),
                    'is_on_sale' => $query->isOnSale(),
                    'in_stock' => $query->isInStock(),
                    'price_range' => [
                        'min' => $query->getMinPrice(),
                        'max' => $query->getMaxPrice(),
                    ],
                    'search' => $query->getSearch(),
                    'attributes' => $query->getAttributes(),
                ],
                'sorting' => [
                    'sort_by' => $query->getSortBy(),
                    'sort_direction' => $query->getSortDirection(),
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get products', [
                'criteria' => $query->getCriteria(),
                'limit' => $query->getLimit(),
                'offset' => $query->getOffset(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
