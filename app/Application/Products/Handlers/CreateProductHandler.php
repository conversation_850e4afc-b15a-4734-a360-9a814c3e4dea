<?php

namespace App\Application\Products\Handlers;

use App\Application\Products\Commands\CreateProductCommand;
use App\Application\Products\DTOs\ProductDTO;
use App\Domain\Products\Entities\Product;
use App\Domain\Products\Entities\ProductAttribute;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Products\ValueObjects\Weight;
use App\Domain\Products\ValueObjects\Dimensions;
use App\Domain\Products\ValueObjects\SEOData;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use App\Domain\Shared\Events\DomainEventDispatcher;
use App\Core\CQRS\Base\BaseCommandHandler;
use App\Core\CQRS\Contracts\CommandInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * CreateProductHandler
 * Yeni ürün oluşturma command handler'ı
 */
class CreateProductHandler extends BaseCommandHandler
{
    public function __construct(
        private ProductRepositoryInterface $productRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    /**
     * Handler'ın desteklediği command sınıfı
     *
     * @return string
     */
    public function getCommandClass(): string
    {
        return CreateProductCommand::class;
    }

    /**
     * Command'ı gerçekten işleyen method
     *
     * @param CommandInterface $command
     * @return ProductDTO
     */
    protected function execute(CommandInterface $command): ProductDTO
    {
        /** @var CreateProductCommand $command */
        return DB::transaction(function () use ($command) {
            try {
                // SKU'nun benzersiz olduğunu kontrol et
                $sku = SKU::fromString($command->getSku());
                if ($this->productRepository->existsBySKU($sku)) {
                    throw new \InvalidArgumentException("SKU '{$command->getSku()}' already exists");
                }

                // Slug'ın benzersiz olduğunu kontrol et
                if ($this->productRepository->existsBySlug($command->getSlug())) {
                    throw new \InvalidArgumentException("Slug '{$command->getSlug()}' already exists");
                }

                // Fiyat oluştur
                $price = Price::fromAmount($command->getPrice(), $command->getCurrency());

                // Stok oluştur
                $stock = new Stock(
                    quantity: $command->getStockQuantity(),
                    lowStockThreshold: $command->getLowStockThreshold(),
                    trackQuantity: $command->isTrackQuantity(),
                    allowBackorders: $command->isAllowBackorders()
                );

                // Ürün oluştur
                $product = Product::create(
                    name: $command->getName(),
                    slug: $command->getSlug(),
                    sku: $sku,
                    price: $price,
                    stock: $stock,
                    categoryId: $command->getCategoryId(),
                    description: $command->getDescription(),
                    status: $command->getStatus(),
                    isFeatured: $command->isFeatured()
                );

                // İndirim fiyatı varsa ekle
                if ($command->hasSalePrice()) {
                    $salePrice = Price::fromAmount($command->getSalePrice(), $command->getCurrency());
                    $startsAt = $command->getSaleStartsAt() ? Carbon::parse($command->getSaleStartsAt()) : null;
                    $endsAt = $command->getSaleEndsAt() ? Carbon::parse($command->getSaleEndsAt()) : null;
                    
                    $product->setSalePrice($salePrice, $startsAt, $endsAt);
                }

                // Ağırlık varsa ekle
                if ($command->hasWeight()) {
                    $weight = new Weight($command->getWeight(), $command->getWeightUnit());
                    $product->setWeight($weight);
                }

                // Boyutlar varsa ekle
                if ($command->hasDimensions()) {
                    $dimensions = new Dimensions(
                        $command->getLength(),
                        $command->getWidth(),
                        $command->getHeight(),
                        $command->getDimensionUnit()
                    );
                    $product->setDimensions($dimensions);
                }

                // SEO verileri varsa ekle
                if ($command->hasSEOData()) {
                    $seoData = SEOData::fromProduct(
                        productName: $command->getName(),
                        productDescription: $command->getDescription() ?? '',
                        price: $command->getPrice(),
                        currency: $command->getCurrency(),
                        metaTitle: $command->getMetaTitle(),
                        metaDescription: $command->getMetaDescription()
                    );
                    
                    if ($command->getMetaTitle()) {
                        $seoData = $seoData->updateMetaTitle($command->getMetaTitle());
                    }
                    
                    if ($command->getMetaDescription()) {
                        $seoData = $seoData->updateMetaDescription($command->getMetaDescription());
                    }
                    
                    $product->setSEOData($seoData);
                }

                // Resimleri ekle
                foreach ($command->getImages() as $index => $imagePath) {
                    $isPrimary = $index === 0; // İlk resim primary
                    $product->addImage($imagePath, $isPrimary);
                }

                // Ürünü kaydet
                $savedProduct = $this->productRepository->save($product);

                // Attribute'ları ekle
                foreach ($command->getAttributes() as $attributeData) {
                    $attribute = ProductAttribute::create(
                        productId: $savedProduct->getId(),
                        attributeId: $attributeData['attribute_id'],
                        value: $attributeData['value'],
                        isVariantGenerator: $attributeData['is_variant_generator'] ?? false,
                        isRequired: $attributeData['is_required'] ?? false,
                        position: $attributeData['position'] ?? null
                    );
                    $savedProduct->addAttribute($attribute);
                }

                // Attribute'lar eklendiyse ürünü tekrar kaydet
                if (!empty($command->getAttributes())) {
                    $savedProduct = $this->productRepository->save($savedProduct);
                }

                // Domain event'leri dispatch et
                $this->eventDispatcher->dispatchEvents($savedProduct);

                Log::info('Product created successfully', [
                    'product_id' => $savedProduct->getId(),
                    'name' => $savedProduct->getName(),
                    'sku' => $savedProduct->getSKU()->getValue(),
                    'price' => $savedProduct->getPrice()->getAmount(),
                    'stock' => $savedProduct->getStock()->getQuantity()
                ]);

                return ProductDTO::fromEntity($savedProduct);

            } catch (\Exception $e) {
                Log::error('Failed to create product', [
                    'name' => $command->getName(),
                    'sku' => $command->getSku(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }
}
