<?php

namespace App\Application\Products\Commands;

use App\Core\CQRS\Base\BaseCommand;

/**
 * CreateProductCommand
 * Yeni ürün oluşturma komutu
 */
class CreateProductCommand extends BaseCommand
{
    public function __construct(
        public readonly string $name,
        public readonly string $slug,
        public readonly string $sku,
        public readonly float $price,
        public readonly int $stockQuantity,
        public readonly int $categoryId,
        public readonly ?string $description = null,
        public readonly string $currency = 'TRY',
        public readonly bool $status = true,
        public readonly bool $isFeatured = false,
        public readonly ?float $salePrice = null,
        public readonly ?string $saleStartsAt = null,
        public readonly ?string $saleEndsAt = null,
        public readonly ?float $weight = null,
        public readonly string $weightUnit = 'g',
        public readonly ?float $length = null,
        public readonly ?float $width = null,
        public readonly ?float $height = null,
        public readonly string $dimensionUnit = 'cm',
        public readonly ?string $metaTitle = null,
        public readonly ?string $metaDescription = null,
        public readonly ?string $metaKeywords = null,
        public readonly array $images = [],
        public readonly array $attributes = [],
        public readonly int $lowStockThreshold = 5,
        public readonly bool $trackQuantity = true,
        public readonly bool $allowBackorders = false
    ) {
        parent::__construct();
    }

    /**
     * Command'ın validation kuralları
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:products,slug',
            'sku' => 'required|string|max:100|unique:products,sku',
            'price' => 'required|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'category_id' => 'required|integer|exists:categories,id',
            'description' => 'nullable|string',
            'currency' => 'required|string|size:3',
            'status' => 'boolean',
            'is_featured' => 'boolean',
            'sale_price' => 'nullable|numeric|min:0|lt:price',
            'sale_starts_at' => 'nullable|date',
            'sale_ends_at' => 'nullable|date|after:sale_starts_at',
            'weight' => 'nullable|numeric|min:0',
            'weight_unit' => 'required|string|in:g,kg',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'height' => 'nullable|numeric|min:0',
            'dimension_unit' => 'required|string|in:cm,m',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'images' => 'array',
            'attributes' => 'array',
            'low_stock_threshold' => 'required|integer|min:0',
            'track_quantity' => 'boolean',
            'allow_backorders' => 'boolean',
        ];
    }

    /**
     * Command'ı array'e dönüştür
     *
     * @return array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'name' => $this->name,
            'slug' => $this->slug,
            'sku' => $this->sku,
            'price' => $this->price,
            'stock_quantity' => $this->stockQuantity,
            'category_id' => $this->categoryId,
            'description' => $this->description,
            'currency' => $this->currency,
            'status' => $this->status,
            'is_featured' => $this->isFeatured,
            'sale_price' => $this->salePrice,
            'sale_starts_at' => $this->saleStartsAt,
            'sale_ends_at' => $this->saleEndsAt,
            'weight' => $this->weight,
            'weight_unit' => $this->weightUnit,
            'length' => $this->length,
            'width' => $this->width,
            'height' => $this->height,
            'dimension_unit' => $this->dimensionUnit,
            'meta_title' => $this->metaTitle,
            'meta_description' => $this->metaDescription,
            'meta_keywords' => $this->metaKeywords,
            'images' => $this->images,
            'attributes' => $this->attributes,
            'low_stock_threshold' => $this->lowStockThreshold,
            'track_quantity' => $this->trackQuantity,
            'allow_backorders' => $this->allowBackorders,
        ]);
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getSku(): string
    {
        return $this->sku;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function getStockQuantity(): int
    {
        return $this->stockQuantity;
    }

    public function getCategoryId(): int
    {
        return $this->categoryId;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function getStatus(): bool
    {
        return $this->status;
    }

    public function isFeatured(): bool
    {
        return $this->isFeatured;
    }

    public function getSalePrice(): ?float
    {
        return $this->salePrice;
    }

    public function getSaleStartsAt(): ?string
    {
        return $this->saleStartsAt;
    }

    public function getSaleEndsAt(): ?string
    {
        return $this->saleEndsAt;
    }

    public function getWeight(): ?float
    {
        return $this->weight;
    }

    public function getWeightUnit(): string
    {
        return $this->weightUnit;
    }

    public function getLength(): ?float
    {
        return $this->length;
    }

    public function getWidth(): ?float
    {
        return $this->width;
    }

    public function getHeight(): ?float
    {
        return $this->height;
    }

    public function getDimensionUnit(): string
    {
        return $this->dimensionUnit;
    }

    public function getMetaTitle(): ?string
    {
        return $this->metaTitle;
    }

    public function getMetaDescription(): ?string
    {
        return $this->metaDescription;
    }

    public function getMetaKeywords(): ?string
    {
        return $this->metaKeywords;
    }

    public function getImages(): array
    {
        return $this->images;
    }

    public function getAttributes(): array
    {
        return $this->attributes;
    }

    public function getLowStockThreshold(): int
    {
        return $this->lowStockThreshold;
    }

    public function isTrackQuantity(): bool
    {
        return $this->trackQuantity;
    }

    public function isAllowBackorders(): bool
    {
        return $this->allowBackorders;
    }

    public function hasSalePrice(): bool
    {
        return $this->salePrice !== null && $this->salePrice > 0;
    }

    public function hasWeight(): bool
    {
        return $this->weight !== null && $this->weight > 0;
    }

    public function hasDimensions(): bool
    {
        return $this->length !== null && $this->width !== null && $this->height !== null &&
               $this->length > 0 && $this->width > 0 && $this->height > 0;
    }

    public function hasSEOData(): bool
    {
        return $this->metaTitle !== null || $this->metaDescription !== null;
    }
}
