<?php

namespace App\Application\Products\Queries;

use App\Core\CQRS\Base\BaseQuery;

/**
 * GetProductsQuery
 * Ürün listesi getirme sorgusu
 */
class GetProductsQuery extends BaseQuery
{
    public function __construct(
        public readonly ?int $categoryId = null,
        public readonly ?bool $status = null,
        public readonly ?bool $isFeatured = null,
        public readonly ?bool $isOnSale = null,
        public readonly ?bool $inStock = null,
        public readonly ?float $minPrice = null,
        public readonly ?float $maxPrice = null,
        public readonly ?string $search = null,
        public readonly ?string $sortBy = 'created_at',
        public readonly string $sortDirection = 'desc',
        public readonly int $limit = 10,
        public readonly int $offset = 0,
        public readonly bool $includeVariants = false,
        public readonly bool $includeAttributes = false,
        public readonly bool $includeImages = false,
        public readonly array $attributes = []
    ) {
        parent::__construct();
    }

    /**
     * Query'nin validation kuralları
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'category_id' => 'nullable|integer|exists:categories,id',
            'status' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_on_sale' => 'nullable|boolean',
            'in_stock' => 'nullable|boolean',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0|gte:min_price',
            'search' => 'nullable|string|max:255',
            'sort_by' => 'nullable|string|in:name,price,created_at,updated_at,stock_quantity',
            'sort_direction' => 'required|string|in:asc,desc',
            'limit' => 'required|integer|min:1|max:100',
            'offset' => 'required|integer|min:0',
            'include_variants' => 'boolean',
            'include_attributes' => 'boolean',
            'include_images' => 'boolean',
            'attributes' => 'array',
        ];
    }

    /**
     * Query'nin cache key'i
     *
     * @return string|null
     */
    public function getCacheKey(): ?string
    {
        $key = 'products:list:' . md5(serialize($this->toArray()));
        return $key;
    }

    /**
     * Query'nin cache TTL'i (saniye)
     *
     * @return int|null
     */
    public function getCacheTtl(): ?int
    {
        return 300; // 5 dakika
    }

    /**
     * Query'yi array'e dönüştür
     *
     * @return array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'category_id' => $this->categoryId,
            'status' => $this->status,
            'is_featured' => $this->isFeatured,
            'is_on_sale' => $this->isOnSale,
            'in_stock' => $this->inStock,
            'min_price' => $this->minPrice,
            'max_price' => $this->maxPrice,
            'search' => $this->search,
            'sort_by' => $this->sortBy,
            'sort_direction' => $this->sortDirection,
            'limit' => $this->limit,
            'offset' => $this->offset,
            'include_variants' => $this->includeVariants,
            'include_attributes' => $this->includeAttributes,
            'include_images' => $this->includeImages,
            'attributes' => $this->attributes,
        ]);
    }

    public function getCategoryId(): ?int
    {
        return $this->categoryId;
    }

    public function getStatus(): ?bool
    {
        return $this->status;
    }

    public function isFeatured(): ?bool
    {
        return $this->isFeatured;
    }

    public function isOnSale(): ?bool
    {
        return $this->isOnSale;
    }

    public function isInStock(): ?bool
    {
        return $this->inStock;
    }

    public function getMinPrice(): ?float
    {
        return $this->minPrice;
    }

    public function getMaxPrice(): ?float
    {
        return $this->maxPrice;
    }

    public function getSearch(): ?string
    {
        return $this->search;
    }

    public function getSortBy(): ?string
    {
        return $this->sortBy;
    }

    public function getSortDirection(): string
    {
        return $this->sortDirection;
    }

    public function getLimit(): int
    {
        return $this->limit;
    }

    public function getOffset(): int
    {
        return $this->offset;
    }

    public function shouldIncludeVariants(): bool
    {
        return $this->includeVariants;
    }

    public function shouldIncludeAttributes(): bool
    {
        return $this->includeAttributes;
    }

    public function shouldIncludeImages(): bool
    {
        return $this->includeImages;
    }

    public function getAttributes(): array
    {
        return $this->attributes;
    }

    public function getCriteria(): array
    {
        $criteria = [];

        if ($this->categoryId !== null) {
            $criteria['category_id'] = $this->categoryId;
        }

        if ($this->status !== null) {
            $criteria['status'] = $this->status;
        }

        if ($this->isFeatured !== null) {
            $criteria['is_featured'] = $this->isFeatured;
        }

        if ($this->isOnSale !== null) {
            $criteria['is_on_sale'] = $this->isOnSale;
        }

        if ($this->inStock !== null) {
            $criteria['in_stock'] = $this->inStock;
        }

        if ($this->minPrice !== null) {
            $criteria['min_price'] = $this->minPrice;
        }

        if ($this->maxPrice !== null) {
            $criteria['max_price'] = $this->maxPrice;
        }

        if ($this->search !== null) {
            $criteria['search'] = $this->search;
        }

        if (!empty($this->attributes)) {
            $criteria['attributes'] = $this->attributes;
        }

        return $criteria;
    }

    public function hasPriceFilter(): bool
    {
        return $this->minPrice !== null || $this->maxPrice !== null;
    }

    public function hasAttributeFilter(): bool
    {
        return !empty($this->attributes);
    }
}
