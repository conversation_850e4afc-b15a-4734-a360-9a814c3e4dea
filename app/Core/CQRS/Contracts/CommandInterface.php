<?php

namespace App\Core\CQRS\Contracts;

/**
 * Command Interface
 * Tüm command'lar bu interface'i implement etmelidir
 */
interface CommandInterface
{
    /**
     * Command'ın benzersiz kimliği
     *
     * @return string
     */
    public function getId(): string;

    /**
     * Command'ın oluşturulma zamanı
     *
     * @return \DateTimeInterface
     */
    public function getCreatedAt(): \DateTimeInterface;

    /**
     * Command'ı serialize et
     *
     * @return array
     */
    public function toArray(): array;

    /**
     * Command'ın validation kuralları
     *
     * @return array
     */
    public function rules(): array;
}
