<?php

namespace App\Core\CQRS\Contracts;

/**
 * Query Interface
 * Tüm query'ler bu interface'i implement etmelidir
 */
interface QueryInterface
{
    /**
     * Query'nin benzersiz kimliği
     *
     * @return string
     */
    public function getId(): string;

    /**
     * Query'nin oluşturulma zamanı
     *
     * @return \DateTimeInterface
     */
    public function getCreatedAt(): \DateTimeInterface;

    /**
     * Query'yi serialize et
     *
     * @return array
     */
    public function toArray(): array;

    /**
     * Query'nin validation kuralları
     *
     * @return array
     */
    public function rules(): array;

    /**
     * Query'nin cache key'i
     *
     * @return string|null
     */
    public function getCacheKey(): ?string;

    /**
     * Query'nin cache TTL'i (saniye)
     *
     * @return int|null
     */
    public function getCacheTtl(): ?int;
}
