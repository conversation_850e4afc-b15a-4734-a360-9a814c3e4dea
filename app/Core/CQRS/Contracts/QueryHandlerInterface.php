<?php

namespace App\Core\CQRS\Contracts;

/**
 * Query Handler Interface
 * Tüm query handler'lar bu interface'i implement etmelidir
 */
interface QueryHandlerInterface
{
    /**
     * Query'yi işle
     *
     * @param QueryInterface $query
     * @return mixed
     * @throws \Exception
     */
    public function handle(QueryInterface $query);

    /**
     * Handler'ın desteklediği query sınıfı
     *
     * @return string
     */
    public function getQueryClass(): string;

    /**
     * Handler'ın öncelik seviyesi (düşük sayı = yüksek öncelik)
     *
     * @return int
     */
    public function getPriority(): int;

    /**
     * Query sonucunun cache'lenip cache'lenmeyeceği
     *
     * @return bool
     */
    public function isCacheable(): bool;
}
