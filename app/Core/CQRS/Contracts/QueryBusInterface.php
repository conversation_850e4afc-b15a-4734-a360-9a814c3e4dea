<?php

namespace App\Core\CQRS\Contracts;

/**
 * Query Bus Interface
 * Query bus implementasyonları bu interface'i implement etmelidir
 */
interface QueryBusInterface
{
    /**
     * Query'yi dispatch et
     *
     * @param QueryInterface $query
     * @return mixed
     * @throws \Exception
     */
    public function dispatch(QueryInterface $query);

    /**
     * Query handler'ı kaydet
     *
     * @param string $queryClass
     * @param string $handlerClass
     * @return void
     */
    public function registerHandler(string $queryClass, string $handlerClass): void;

    /**
     * Middleware ekle
     *
     * @param callable $middleware
     * @return void
     */
    public function addMiddleware(callable $middleware): void;

    /**
     * Kayıtlı handler'ları al
     *
     * @return array
     */
    public function getRegisteredHandlers(): array;

    /**
     * Cache'den sonuç al
     *
     * @param QueryInterface $query
     * @return mixed|null
     */
    public function getFromCache(QueryInterface $query);

    /**
     * Sonucu cache'le
     *
     * @param QueryInterface $query
     * @param mixed $result
     * @return void
     */
    public function putToCache(QueryInterface $query, $result): void;
}
