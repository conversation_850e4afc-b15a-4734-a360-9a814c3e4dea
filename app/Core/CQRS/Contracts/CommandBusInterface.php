<?php

namespace App\Core\CQRS\Contracts;

/**
 * Command Bus Interface
 * Command bus implementasyonları bu interface'i implement etmelidir
 */
interface CommandBusInterface
{
    /**
     * Command'ı dispatch et
     *
     * @param CommandInterface $command
     * @return mixed
     * @throws \Exception
     */
    public function dispatch(CommandInterface $command);

    /**
     * Command handler'ı kaydet
     *
     * @param string $commandClass
     * @param string $handlerClass
     * @return void
     */
    public function registerHandler(string $commandClass, string $handlerClass): void;

    /**
     * Middleware ekle
     *
     * @param callable $middleware
     * @return void
     */
    public function addMiddleware(callable $middleware): void;

    /**
     * Kay<PERSON><PERSON><PERSON> handler'ları al
     *
     * @return array
     */
    public function getRegisteredHandlers(): array;
}
