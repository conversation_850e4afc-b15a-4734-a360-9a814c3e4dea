<?php

namespace App\Core\CQRS\Base;

use App\Core\CQRS\Contracts\CommandInterface;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * Base Command sınıfı
 * Tüm command'lar bu sınıftan türetilmelidir
 */
abstract class BaseCommand implements CommandInterface
{
    protected string $id;
    protected \DateTimeInterface $createdAt;

    public function __construct()
    {
        $this->id = Str::uuid()->toString();
        $this->createdAt = Carbon::now();
    }

    /**
     * Command'ın benzersiz kimliği
     *
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * Command'ın oluşturulma zamanı
     *
     * @return \DateTimeInterface
     */
    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    /**
     * Command'ı serialize et
     *
     * @return array
     */
    public function toArray(): array
    {
        $reflection = new \ReflectionClass($this);
        $properties = $reflection->getProperties(\ReflectionProperty::IS_PUBLIC | \ReflectionProperty::IS_PROTECTED);
        
        $data = [
            'id' => $this->id,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'command_class' => get_class($this),
        ];

        foreach ($properties as $property) {
            $property->setAccessible(true);
            $name = $property->getName();
            
            if (!in_array($name, ['id', 'createdAt'])) {
                $data[$name] = $property->getValue($this);
            }
        }

        return $data;
    }

    /**
     * Command'ın validation kuralları
     * Override edilmelidir
     *
     * @return array
     */
    public function rules(): array
    {
        return [];
    }

    /**
     * Command'ı validate et
     *
     * @return void
     * @throws \InvalidArgumentException
     */
    public function validate(): void
    {
        $rules = $this->rules();
        
        if (empty($rules)) {
            return;
        }

        $validator = validator($this->toArray(), $rules);
        
        if ($validator->fails()) {
            throw new \InvalidArgumentException(
                'Command validation failed: ' . implode(', ', $validator->errors()->all())
            );
        }
    }
}
