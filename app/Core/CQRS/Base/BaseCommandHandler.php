<?php

namespace App\Core\CQRS\Base;

use App\Core\CQRS\Contracts\CommandHandlerInterface;
use App\Core\CQRS\Contracts\CommandInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * Base Command Handler sınıfı
 * Tüm command handler'lar bu sınıftan türetilmelidir
 */
abstract class BaseCommandHandler implements CommandHandlerInterface
{
    /**
     * Command'ı işle
     *
     * @param CommandInterface $command
     * @return mixed
     * @throws \Exception
     */
    public function handle(CommandInterface $command)
    {
        // Command validation
        $command->validate();

        // Log command execution
        $this->logCommandExecution($command);

        try {
            // Transaction içinde çalıştır
            return DB::transaction(function () use ($command) {
                return $this->execute($command);
            });
        } catch (\Exception $e) {
            $this->logCommandError($command, $e);
            throw $e;
        }
    }

    /**
     * Command'ı gerçekten işleyen method
     * Alt sınıflarda implement edilmelidir
     *
     * @param CommandInterface $command
     * @return mixed
     */
    abstract protected function execute(CommandInterface $command);

    /**
     * Handler'ın öncelik seviyesi (düşük sayı = yüksek öncelik)
     *
     * @return int
     */
    public function getPriority(): int
    {
        return 100;
    }

    /**
     * Command execution'ını logla
     *
     * @param CommandInterface $command
     * @return void
     */
    protected function logCommandExecution(CommandInterface $command): void
    {
        Log::info('Command executed', [
            'command_id' => $command->getId(),
            'command_class' => get_class($command),
            'handler_class' => get_class($this),
            'created_at' => $command->getCreatedAt()->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Command error'ını logla
     *
     * @param CommandInterface $command
     * @param \Exception $exception
     * @return void
     */
    protected function logCommandError(CommandInterface $command, \Exception $exception): void
    {
        Log::error('Command execution failed', [
            'command_id' => $command->getId(),
            'command_class' => get_class($command),
            'handler_class' => get_class($this),
            'error_message' => $exception->getMessage(),
            'error_trace' => $exception->getTraceAsString(),
        ]);
    }
}
