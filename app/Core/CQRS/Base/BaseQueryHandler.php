<?php

namespace App\Core\CQRS\Base;

use App\Core\CQRS\Contracts\QueryHandlerInterface;
use App\Core\CQRS\Contracts\QueryInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Base Query Handler sınıfı
 * Tüm query handler'lar bu sınıftan türetilmelidir
 */
abstract class BaseQueryHandler implements QueryHandlerInterface
{
    /**
     * Query'yi işle
     *
     * @param QueryInterface $query
     * @return mixed
     * @throws \Exception
     */
    public function handle(QueryInterface $query)
    {
        // Query validation
        $query->validate();

        // Log query execution
        $this->logQueryExecution($query);

        try {
            // Cache kontrolü
            if ($this->isCacheable() && $query->getCacheKey()) {
                $cached = Cache::get($query->getCacheKey());
                if ($cached !== null) {
                    $this->logCacheHit($query);
                    return $cached;
                }
            }

            // Query'yi çalıştır
            $result = $this->execute($query);

            // Sonucu cache'le
            if ($this->isCacheable() && $query->getCacheKey() && $query->getCacheTtl()) {
                Cache::put($query->getCacheKey(), $result, $query->getCacheTtl());
                $this->logCacheMiss($query);
            }

            return $result;
        } catch (\Exception $e) {
            $this->logQueryError($query, $e);
            throw $e;
        }
    }

    /**
     * Query'yi gerçekten işleyen method
     * Alt sınıflarda implement edilmelidir
     *
     * @param QueryInterface $query
     * @return mixed
     */
    abstract protected function execute(QueryInterface $query);

    /**
     * Handler'ın öncelik seviyesi (düşük sayı = yüksek öncelik)
     *
     * @return int
     */
    public function getPriority(): int
    {
        return 100;
    }

    /**
     * Query sonucunun cache'lenip cache'lenmeyeceği
     *
     * @return bool
     */
    public function isCacheable(): bool
    {
        return false;
    }

    /**
     * Query execution'ını logla
     *
     * @param QueryInterface $query
     * @return void
     */
    protected function logQueryExecution(QueryInterface $query): void
    {
        Log::info('Query executed', [
            'query_id' => $query->getId(),
            'query_class' => get_class($query),
            'handler_class' => get_class($this),
            'created_at' => $query->getCreatedAt()->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Cache hit'i logla
     *
     * @param QueryInterface $query
     * @return void
     */
    protected function logCacheHit(QueryInterface $query): void
    {
        Log::debug('Query cache hit', [
            'query_id' => $query->getId(),
            'cache_key' => $query->getCacheKey(),
        ]);
    }

    /**
     * Cache miss'i logla
     *
     * @param QueryInterface $query
     * @return void
     */
    protected function logCacheMiss(QueryInterface $query): void
    {
        Log::debug('Query cache miss', [
            'query_id' => $query->getId(),
            'cache_key' => $query->getCacheKey(),
            'cache_ttl' => $query->getCacheTtl(),
        ]);
    }

    /**
     * Query error'ını logla
     *
     * @param QueryInterface $query
     * @param \Exception $exception
     * @return void
     */
    protected function logQueryError(QueryInterface $query, \Exception $exception): void
    {
        Log::error('Query execution failed', [
            'query_id' => $query->getId(),
            'query_class' => get_class($query),
            'handler_class' => get_class($this),
            'error_message' => $exception->getMessage(),
            'error_trace' => $exception->getTraceAsString(),
        ]);
    }
}
