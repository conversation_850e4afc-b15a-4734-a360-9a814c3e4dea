<?php

namespace App\Core\CQRS\Base;

use App\Core\CQRS\Contracts\QueryInterface;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * Base Query sınıfı
 * Tüm query'ler bu sınıftan türetilmelidir
 */
abstract class BaseQuery implements QueryInterface
{
    protected string $id;
    protected \DateTimeInterface $createdAt;

    public function __construct()
    {
        $this->id = Str::uuid()->toString();
        $this->createdAt = Carbon::now();
    }

    /**
     * Query'nin benzersiz kim<PERSON>i
     *
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * Query'nin oluşturulma zamanı
     *
     * @return \DateTimeInterface
     */
    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    /**
     * Query'yi serialize et
     *
     * @return array
     */
    public function toArray(): array
    {
        $reflection = new \ReflectionClass($this);
        $properties = $reflection->getProperties(\ReflectionProperty::IS_PUBLIC | \ReflectionProperty::IS_PROTECTED);
        
        $data = [
            'id' => $this->id,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'query_class' => get_class($this),
        ];

        foreach ($properties as $property) {
            $property->setAccessible(true);
            $name = $property->getName();
            
            if (!in_array($name, ['id', 'createdAt'])) {
                $data[$name] = $property->getValue($this);
            }
        }

        return $data;
    }

    /**
     * Query'nin validation kuralları
     * Override edilmelidir
     *
     * @return array
     */
    public function rules(): array
    {
        return [];
    }

    /**
     * Query'nin cache key'i
     * Override edilebilir
     *
     * @return string|null
     */
    public function getCacheKey(): ?string
    {
        return null;
    }

    /**
     * Query'nin cache TTL'i (saniye)
     * Override edilebilir
     *
     * @return int|null
     */
    public function getCacheTtl(): ?int
    {
        return null;
    }

    /**
     * Query'yi validate et
     *
     * @return void
     * @throws \InvalidArgumentException
     */
    public function validate(): void
    {
        $rules = $this->rules();
        
        if (empty($rules)) {
            return;
        }

        $validator = validator($this->toArray(), $rules);
        
        if ($validator->fails()) {
            throw new \InvalidArgumentException(
                'Query validation failed: ' . implode(', ', $validator->errors()->all())
            );
        }
    }
}
