import React from 'react';

/**
 * Error Boundary Component
 * Phase 4C: Component Architecture İyileştirmeleri
 */
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { 
            hasError: false, 
            error: null, 
            errorInfo: null 
        };
    }

    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        // Log error details
        this.setState({
            error: error,
            errorInfo: errorInfo
        });

        // Log to console in development
        if (process.env.NODE_ENV === 'development') {
            console.error('ErrorBoundary caught an error:', error, errorInfo);
        }

        // You can also log the error to an error reporting service here
        this.logErrorToService(error, errorInfo);
    }

    logErrorToService(error, errorInfo) {
        // Example: Send error to logging service
        // This could be Sentry, LogRocket, or your own logging endpoint
        try {
            if (window.errorLogger) {
                window.errorLogger.log({
                    error: error.toString(),
                    stack: error.stack,
                    componentStack: errorInfo.componentStack,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                });
            }
        } catch (loggingError) {
            console.error('Failed to log error:', loggingError);
        }
    }

    handleRetry = () => {
        this.setState({ 
            hasError: false, 
            error: null, 
            errorInfo: null 
        });
    };

    render() {
        if (this.state.hasError) {
            // Custom fallback UI
            if (this.props.fallback) {
                if (typeof this.props.fallback === 'function') {
                    return this.props.fallback(this.state.error, this.handleRetry);
                }
                return this.props.fallback;
            }

            // Default fallback UI
            return (
                <div className="error-boundary bg-red-50 border border-red-200 rounded-lg p-6 m-4">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            <svg 
                                className="h-6 w-6 text-red-400" 
                                fill="none" 
                                viewBox="0 0 24 24" 
                                stroke="currentColor"
                            >
                                <path 
                                    strokeLinecap="round" 
                                    strokeLinejoin="round" 
                                    strokeWidth={2} 
                                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                                />
                            </svg>
                        </div>
                        <div className="ml-3 flex-1">
                            <h3 className="text-sm font-medium text-red-800">
                                Bir hata oluştu
                            </h3>
                            <div className="mt-2 text-sm text-red-700">
                                <p>
                                    Bu bileşen yüklenirken beklenmeyen bir hata oluştu. 
                                    Lütfen sayfayı yenilemeyi deneyin.
                                </p>
                                {process.env.NODE_ENV === 'development' && this.state.error && (
                                    <details className="mt-4">
                                        <summary className="cursor-pointer font-medium">
                                            Hata Detayları (Geliştirici Modu)
                                        </summary>
                                        <div className="mt-2 p-3 bg-red-100 rounded border text-xs font-mono">
                                            <div className="mb-2">
                                                <strong>Error:</strong> {this.state.error.toString()}
                                            </div>
                                            {this.state.error.stack && (
                                                <div className="mb-2">
                                                    <strong>Stack:</strong>
                                                    <pre className="whitespace-pre-wrap mt-1">
                                                        {this.state.error.stack}
                                                    </pre>
                                                </div>
                                            )}
                                            {this.state.errorInfo && this.state.errorInfo.componentStack && (
                                                <div>
                                                    <strong>Component Stack:</strong>
                                                    <pre className="whitespace-pre-wrap mt-1">
                                                        {this.state.errorInfo.componentStack}
                                                    </pre>
                                                </div>
                                            )}
                                        </div>
                                    </details>
                                )}
                            </div>
                            <div className="mt-4">
                                <div className="flex space-x-3">
                                    <button
                                        type="button"
                                        onClick={this.handleRetry}
                                        className="bg-red-600 text-white text-sm px-3 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                    >
                                        Tekrar Dene
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => window.location.reload()}
                                        className="bg-white text-red-600 text-sm px-3 py-2 border border-red-300 rounded-md hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                    >
                                        Sayfayı Yenile
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

/**
 * Higher-Order Component for Error Boundary
 */
export function withErrorBoundary(Component, fallback) {
    return function WrappedComponent(props) {
        return (
            <ErrorBoundary fallback={fallback}>
                <Component {...props} />
            </ErrorBoundary>
        );
    };
}

/**
 * Hook for Error Boundary (React 18+)
 */
export function useErrorHandler() {
    const [error, setError] = React.useState(null);

    const resetError = React.useCallback(() => {
        setError(null);
    }, []);

    const captureError = React.useCallback((error) => {
        setError(error);
    }, []);

    React.useEffect(() => {
        if (error) {
            throw error;
        }
    }, [error]);

    return { captureError, resetError };
}

/**
 * Simple Error Display Component
 */
export function ErrorDisplay({ 
    error, 
    onRetry, 
    title = "Bir hata oluştu",
    className = "" 
}) {
    return (
        <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
            <div className="flex">
                <div className="flex-shrink-0">
                    <svg 
                        className="h-5 w-5 text-red-400" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                    >
                        <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth={2} 
                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                        />
                    </svg>
                </div>
                <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                        {title}
                    </h3>
                    {error && (
                        <div className="mt-2 text-sm text-red-700">
                            <p>{error.message || error.toString()}</p>
                        </div>
                    )}
                    {onRetry && (
                        <div className="mt-4">
                            <button
                                type="button"
                                onClick={onRetry}
                                className="bg-red-600 text-white text-sm px-3 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                            >
                                Tekrar Dene
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

export default ErrorBoundary;
