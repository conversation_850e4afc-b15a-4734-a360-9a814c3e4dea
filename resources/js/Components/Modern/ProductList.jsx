import React, { useState, useCallback, useMemo } from 'react';
import { useProducts } from '../../Hooks/useApiData.js';
import { useApi } from '../../Contexts/ApiContext.jsx';
import LoadingSpinner from '../ui/LoadingSpinner.jsx';
import ErrorBoundary from '../ui/ErrorBoundary.jsx';
import ProductCard from '../ProductCard.jsx';
import Pagination from '../Pagination.jsx';

/**
 * Modern Product List Component
 * Phase 4C: Component Architecture İyileştirmeleri
 * 
 * CQRS pattern'e uygun, modern React patterns kullanır
 */
function ProductList({ 
    initialFilters = {},
    showFilters = true,
    showPagination = true,
    showSorting = true,
    pageSize = 12,
    layout = 'grid', // 'grid' | 'list'
    className = ''
}) {
    const [localFilters, setLocalFilters] = useState(initialFilters);
    const [sortBy, setSortBy] = useState('created_at');
    const [sortDirection, setSortDirection] = useState('desc');
    const [currentPage, setCurrentPage] = useState(1);
    
    // API state management
    const { config } = useApi();
    
    // Prepare filters for API call
    const apiFilters = useMemo(() => ({
        ...localFilters,
        sort_by: sortBy,
        sort_direction: sortDirection,
        limit: pageSize,
        offset: (currentPage - 1) * pageSize
    }), [localFilters, sortBy, sortDirection, pageSize, currentPage]);
    
    // Fetch products using custom hook
    const {
        products,
        loading,
        error,
        pagination,
        fetchProducts,
        refetch
    } = useProducts(apiFilters, true);
    
    // Event handlers
    const handleFilterChange = useCallback((newFilters) => {
        setLocalFilters(prev => ({ ...prev, ...newFilters }));
        setCurrentPage(1); // Reset to first page when filters change
    }, []);
    
    const handleSortChange = useCallback((field, direction = 'asc') => {
        setSortBy(field);
        setSortDirection(direction);
        setCurrentPage(1);
    }, []);
    
    const handlePageChange = useCallback((page) => {
        setCurrentPage(page);
    }, []);
    
    const handleRefresh = useCallback(() => {
        refetch();
    }, [refetch]);
    
    // Clear filters
    const handleClearFilters = useCallback(() => {
        setLocalFilters({});
        setSortBy('created_at');
        setSortDirection('desc');
        setCurrentPage(1);
    }, []);
    
    // Render loading state
    if (loading && !products) {
        return (
            <div className="flex justify-center items-center py-12">
                <LoadingSpinner size="lg" />
                <span className="ml-3 text-gray-600">Ürünler yükleniyor...</span>
            </div>
        );
    }
    
    // Render error state
    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                <div className="text-red-600 mb-4">
                    <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="text-lg font-semibold">Ürünler yüklenirken hata oluştu</h3>
                    <p className="text-sm mt-1">{error}</p>
                </div>
                <button
                    onClick={handleRefresh}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                >
                    Tekrar Dene
                </button>
            </div>
        );
    }
    
    // Render empty state
    if (!products || products.length === 0) {
        return (
            <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                    <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">Ürün bulunamadı</h3>
                    <p className="text-gray-500">Arama kriterlerinizi değiştirmeyi deneyin.</p>
                </div>
                {Object.keys(localFilters).length > 0 && (
                    <button
                        onClick={handleClearFilters}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Filtreleri Temizle
                    </button>
                )}
            </div>
        );
    }
    
    return (
        <div className={`product-list ${className}`}>
            {/* Header */}
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center space-x-4">
                    <h2 className="text-xl font-semibold text-gray-800">
                        Ürünler
                        {pagination?.total && (
                            <span className="text-sm font-normal text-gray-500 ml-2">
                                ({pagination.total} ürün)
                            </span>
                        )}
                    </h2>
                    
                    {/* API Version Badge */}
                    {config.useCQRS && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            CQRS
                        </span>
                    )}
                    
                    {config.version && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            API {config.version}
                        </span>
                    )}
                </div>
                
                <div className="flex items-center space-x-3">
                    {/* Refresh Button */}
                    <button
                        onClick={handleRefresh}
                        disabled={loading}
                        className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                        title="Yenile"
                    >
                        <svg className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                    </button>
                    
                    {/* Layout Toggle */}
                    <div className="flex bg-gray-100 rounded-lg p-1">
                        <button
                            onClick={() => setLayout('grid')}
                            className={`p-2 rounded ${layout === 'grid' ? 'bg-white shadow-sm' : 'text-gray-600'}`}
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                            </svg>
                        </button>
                        <button
                            onClick={() => setLayout('list')}
                            className={`p-2 rounded ${layout === 'list' ? 'bg-white shadow-sm' : 'text-gray-600'}`}
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            {/* Sorting */}
            {showSorting && (
                <div className="mb-6">
                    <div className="flex items-center space-x-4">
                        <span className="text-sm text-gray-600">Sıralama:</span>
                        <select
                            value={`${sortBy}-${sortDirection}`}
                            onChange={(e) => {
                                const [field, direction] = e.target.value.split('-');
                                handleSortChange(field, direction);
                            }}
                            className="border border-gray-300 rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="created_at-desc">En Yeni</option>
                            <option value="created_at-asc">En Eski</option>
                            <option value="name-asc">İsim (A-Z)</option>
                            <option value="name-desc">İsim (Z-A)</option>
                            <option value="price-asc">Fiyat (Düşük-Yüksek)</option>
                            <option value="price-desc">Fiyat (Yüksek-Düşük)</option>
                        </select>
                    </div>
                </div>
            )}
            
            {/* Products Grid/List */}
            <div className={`
                ${layout === 'grid' 
                    ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6' 
                    : 'space-y-4'
                }
            `}>
                {products.map((product) => (
                    <ErrorBoundary key={product.id} fallback={<div>Ürün yüklenirken hata oluştu</div>}>
                        <ProductCard 
                            product={product} 
                            layout={layout}
                            showQuickView={true}
                            showAddToCart={true}
                        />
                    </ErrorBoundary>
                ))}
            </div>
            
            {/* Loading overlay for pagination */}
            {loading && products && (
                <div className="relative">
                    <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                        <LoadingSpinner size="md" />
                    </div>
                </div>
            )}
            
            {/* Pagination */}
            {showPagination && pagination && pagination.total > pageSize && (
                <div className="mt-8">
                    <Pagination
                        currentPage={currentPage}
                        totalPages={Math.ceil(pagination.total / pageSize)}
                        onPageChange={handlePageChange}
                        showInfo={true}
                        totalItems={pagination.total}
                        itemsPerPage={pageSize}
                    />
                </div>
            )}
        </div>
    );
}

export default ProductList;
