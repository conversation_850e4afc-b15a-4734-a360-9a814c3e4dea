import api, { legacyApi } from './api.js';
import cqrsApi from './CQRSApiService.js';

/**
 * API Adapter Pattern Implementation
 * Phase 4A: API Integration Layer
 * 
 * Bu adapter legacy API'den yeni CQRS API'ye geçişi yönetir
 */
class ApiAdapter {
    constructor() {
        this.useCQRS = this.shouldUseCQRS();
        this.useNewAPI = this.shouldUseNewAPI();
        this.featureFlags = this.getFeatureFlags();
    }

    /**
     * Feature flag'lere göre CQRS kullanılıp kullanılmayacağını belirle
     */
    shouldUseCQRS() {
        // Environment variable kontrolü
        if (typeof window !== 'undefined' && window.APP_CONFIG) {
            return window.APP_CONFIG.USE_CQRS === true;
        }
        
        // Default olarak false (aşamalı geçiş için)
        return false;
    }

    /**
     * Yeni API kullanılıp kullanılmayacağını belirle
     */
    shouldUseNewAPI() {
        if (typeof window !== 'undefined' && window.APP_CONFIG) {
            return window.APP_CONFIG.USE_NEW_API === true;
        }
        
        return true; // Default olarak yeni API kullan
    }

    /**
     * Feature flag'leri al
     */
    getFeatureFlags() {
        if (typeof window !== 'undefined' && window.FEATURE_FLAGS) {
            return window.FEATURE_FLAGS;
        }
        
        return {};
    }

    /**
     * Hangi API service'ini kullanacağını belirle
     */
    getApiService() {
        if (this.useCQRS) {
            return cqrsApi;
        } else if (this.useNewAPI) {
            return api;
        } else {
            return legacyApi;
        }
    }

    // ==================== PRODUCT OPERATIONS ====================

    /**
     * Ürün listesi getir
     */
    async getProducts(filters = {}) {
        if (this.useCQRS) {
            return await cqrsApi.getProducts(filters);
        } else if (this.useNewAPI) {
            return await api.get('/products', filters);
        } else {
            // Legacy API format
            const response = await legacyApi.get('/api/products', { params: filters });
            return this.normalizeLegacyResponse(response);
        }
    }

    /**
     * Tek ürün getir
     */
    async getProduct(productId, options = {}) {
        if (this.useCQRS) {
            return await cqrsApi.getProduct(productId, options);
        } else if (this.useNewAPI) {
            const params = {
                include_variants: options.includeVariants,
                include_attributes: options.includeAttributes,
                include_images: options.includeImages
            };
            return await api.get(`/products/${productId}`, params);
        } else {
            // Legacy API
            const response = await legacyApi.get(`/api/products/${productId}`);
            return this.normalizeLegacyResponse(response);
        }
    }

    /**
     * Ürün oluştur
     */
    async createProduct(productData) {
        if (this.useCQRS) {
            return await cqrsApi.createProduct(productData);
        } else if (this.useNewAPI) {
            return await api.post('/products', productData);
        } else {
            // Legacy API
            const response = await legacyApi.post('/api/products', productData);
            return this.normalizeLegacyResponse(response);
        }
    }

    /**
     * Ürün güncelle
     */
    async updateProduct(productId, productData) {
        if (this.useCQRS) {
            return await cqrsApi.updateProduct(productId, productData);
        } else if (this.useNewAPI) {
            return await api.put(`/products/${productId}`, productData);
        } else {
            // Legacy API
            const response = await legacyApi.put(`/api/products/${productId}`, productData);
            return this.normalizeLegacyResponse(response);
        }
    }

    /**
     * Ürün sil
     */
    async deleteProduct(productId) {
        if (this.useCQRS) {
            return await cqrsApi.deleteProduct(productId);
        } else if (this.useNewAPI) {
            return await api.delete(`/products/${productId}`);
        } else {
            // Legacy API
            const response = await legacyApi.delete(`/api/products/${productId}`);
            return this.normalizeLegacyResponse(response);
        }
    }

    /**
     * Ürün arama
     */
    async searchProducts(searchTerm, filters = {}) {
        if (this.useCQRS) {
            return await cqrsApi.searchProducts(searchTerm, filters);
        } else if (this.useNewAPI) {
            return await api.get('/products/search', { search: searchTerm, ...filters });
        } else {
            // Legacy API
            const response = await legacyApi.get('/api/products/search', { 
                params: { search: searchTerm, ...filters } 
            });
            return this.normalizeLegacyResponse(response);
        }
    }

    // ==================== CATEGORY OPERATIONS ====================

    /**
     * Kategori listesi getir
     */
    async getCategories(filters = {}) {
        if (this.useCQRS) {
            return await cqrsApi.getCategories(filters);
        } else if (this.useNewAPI) {
            return await api.get('/categories', filters);
        } else {
            // Legacy API
            const response = await legacyApi.get('/api/categories', { params: filters });
            return this.normalizeLegacyResponse(response);
        }
    }

    /**
     * Kategori ağacı getir
     */
    async getCategoryTree() {
        if (this.useCQRS) {
            return await cqrsApi.getCategoryTree();
        } else if (this.useNewAPI) {
            return await api.get('/categories/tree');
        } else {
            // Legacy API
            const response = await legacyApi.get('/api/categories/tree');
            return this.normalizeLegacyResponse(response);
        }
    }

    // ==================== ORDER OPERATIONS ====================

    /**
     * Sipariş listesi getir
     */
    async getOrders(filters = {}) {
        if (this.useCQRS) {
            return await cqrsApi.getOrders(filters);
        } else if (this.useNewAPI) {
            return await api.get('/orders', filters);
        } else {
            // Legacy API
            const response = await legacyApi.get('/api/orders', { params: filters });
            return this.normalizeLegacyResponse(response);
        }
    }

    /**
     * Tek sipariş getir
     */
    async getOrder(orderId) {
        if (this.useCQRS) {
            return await cqrsApi.getOrder(orderId);
        } else if (this.useNewAPI) {
            return await api.get(`/orders/${orderId}`);
        } else {
            // Legacy API
            const response = await legacyApi.get(`/api/orders/${orderId}`);
            return this.normalizeLegacyResponse(response);
        }
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Legacy response'ları normalize et
     */
    normalizeLegacyResponse(response) {
        // Legacy API response format'ını yeni format'a çevir
        if (response.data) {
            return {
                success: true,
                data: response.data.data || response.data,
                message: response.data.message || 'Success',
                meta: response.data.meta || {},
                status: response.status
            };
        }

        return {
            success: true,
            data: response,
            message: 'Success',
            meta: {},
            status: 200
        };
    }

    /**
     * API durumunu kontrol et
     */
    async checkApiHealth() {
        const results = {};

        try {
            if (this.useCQRS) {
                results.cqrs = await cqrsApi.getCQRSStatus();
            }
            
            if (this.useNewAPI) {
                results.newApi = await api.get('/health');
            }
            
            // Legacy API health check
            results.legacy = await legacyApi.get('/api/health');
        } catch (error) {
            console.error('API Health Check Error:', error);
        }

        return results;
    }

    /**
     * Feature flag'leri güncelle
     */
    updateFeatureFlags(flags) {
        this.featureFlags = { ...this.featureFlags, ...flags };
        this.useCQRS = flags.USE_CQRS !== undefined ? flags.USE_CQRS : this.useCQRS;
        this.useNewAPI = flags.USE_NEW_API !== undefined ? flags.USE_NEW_API : this.useNewAPI;
    }

    /**
     * Mevcut konfigürasyonu al
     */
    getConfiguration() {
        return {
            useCQRS: this.useCQRS,
            useNewAPI: this.useNewAPI,
            featureFlags: this.featureFlags,
            apiService: this.getApiService().constructor.name
        };
    }
}

// Create default instance
const apiAdapter = new ApiAdapter();

export default apiAdapter;
export { ApiAdapter };
