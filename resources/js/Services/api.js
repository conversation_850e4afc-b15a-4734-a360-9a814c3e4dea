import axios from "axios";

/**
 * Modern API Service with versioning support
 * Phase 4A: API Integration Layer
 */
class ApiService {
    constructor(version = 'v1.1') {
        this.version = version;
        this.client = this.createClient();
        this.setupInterceptors();
    }

    createClient() {
        return axios.create({
            baseURL: `/api/${this.version}`,
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
                "X-Requested-With": "XMLHttpRequest",
            },
            timeout: 10000, // 10 saniye timeout
        });
    }

    setupInterceptors() {
        // Request interceptor
        this.client.interceptors.request.use(
            (config) => {
                // CSRF token ekleme
                const token = document.head.querySelector('meta[name="csrf-token"]');
                if (token) {
                    config.headers["X-CSRF-TOKEN"] = token.content;
                }

                // API version header ekleme
                config.headers["X-API-Version"] = this.version;

                // Request logging (development)
                if (process.env.NODE_ENV === 'development') {
                    console.log(`API Request [${this.version}]:`, {
                        method: config.method?.toUpperCase(),
                        url: config.url,
                        data: config.data
                    });
                }

                return config;
            },
            (error) => {
                console.error('Request Error:', error);
                return Promise.reject(error);
            }
        );

        // Response interceptor
        this.client.interceptors.response.use(
            (response) => {
                // Response logging (development)
                if (process.env.NODE_ENV === 'development') {
                    console.log(`API Response [${this.version}]:`, {
                        status: response.status,
                        url: response.config.url,
                        data: response.data
                    });
                }

                return response;
            },
            (error) => {
                // Error handling
                if (error.response) {
                    const { status, data } = error.response;

                    switch (status) {
                        case 401:
                            // Unauthorized - redirect to login
                            window.location = "/login";
                            break;
                        case 403:
                            // Forbidden
                            console.error('Access denied:', data.message);
                            break;
                        case 422:
                            // Validation errors
                            console.error('Validation errors:', data.errors);
                            break;
                        case 429:
                            // Rate limiting
                            console.error('Rate limit exceeded');
                            break;
                        case 500:
                            // Server error
                            console.error('Server error:', data.message);
                            break;
                        default:
                            console.error('API Error:', data.message || 'Unknown error');
                    }
                } else if (error.request) {
                    // Network error
                    console.error('Network error:', error.message);
                } else {
                    console.error('Request setup error:', error.message);
                }

                return Promise.reject(error);
            }
        );
    }

    // Generic HTTP methods
    async get(url, params = {}, config = {}) {
        try {
            const response = await this.client.get(url, { params, ...config });
            return this.normalizeResponse(response);
        } catch (error) {
            throw this.normalizeError(error);
        }
    }

    async post(url, data = {}, config = {}) {
        try {
            const response = await this.client.post(url, data, config);
            return this.normalizeResponse(response);
        } catch (error) {
            throw this.normalizeError(error);
        }
    }

    async put(url, data = {}, config = {}) {
        try {
            const response = await this.client.put(url, data, config);
            return this.normalizeResponse(response);
        } catch (error) {
            throw this.normalizeError(error);
        }
    }

    async delete(url, config = {}) {
        try {
            const response = await this.client.delete(url, config);
            return this.normalizeResponse(response);
        } catch (error) {
            throw this.normalizeError(error);
        }
    }

    // Response normalization
    normalizeResponse(response) {
        return {
            success: true,
            data: response.data.data || response.data,
            message: response.data.message || 'Success',
            meta: response.data.meta || {},
            status: response.status
        };
    }

    // Error normalization
    normalizeError(error) {
        if (error.response) {
            return {
                success: false,
                message: error.response.data.message || 'API Error',
                errors: error.response.data.errors || {},
                status: error.response.status,
                data: null
            };
        }

        return {
            success: false,
            message: error.message || 'Network Error',
            errors: {},
            status: 0,
            data: null
        };
    }

    // Version switching
    switchVersion(version) {
        this.version = version;
        this.client = this.createClient();
        this.setupInterceptors();
    }

    // Get current version
    getVersion() {
        return this.version;
    }
}

// Create default instance
const api = new ApiService();

// Legacy compatibility
const legacyApi = axios.create({
    baseURL: "",
    headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "X-Requested-With": "XMLHttpRequest",
    },
});

// Legacy interceptors
legacyApi.interceptors.request.use((config) => {
    const token = document.head.querySelector('meta[name="csrf-token"]');
    if (token) {
        config.headers["X-CSRF-TOKEN"] = token.content;
    }
    return config;
});

legacyApi.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response && error.response.status === 401) {
            window.location = "/login";
        }
        return Promise.reject(error);
    }
);

export default api;
export { ApiService, legacyApi };
