import { useCallback, useEffect, useState } from 'react';
import { useApi } from '../Contexts/ApiContext.jsx';
import apiAdapter from '../Services/ApiAdapter.js';

/**
 * Custom Hooks for API Data Management
 * Phase 4B: State Management Modernizasyonu
 */

/**
 * Generic API data fetching hook
 */
export function useApiData(key, fetchFunction, dependencies = []) {
    const { 
        cache, 
        loading, 
        errors, 
        setLoading, 
        setError, 
        clearError,
        updateCache 
    } = useApi();
    
    const [localLoading, setLocalLoading] = useState(false);
    const [localError, setLocalError] = useState(null);
    
    const fetchData = useCallback(async (...args) => {
        try {
            setLoading(key, true);
            setLocalLoading(true);
            clearError(key);
            setLocalError(null);
            
            const result = await fetchFunction(...args);
            
            if (result.success) {
                updateCache(key, result.data);
                return result;
            } else {
                throw new Error(result.message || 'API Error');
            }
        } catch (error) {
            const errorMessage = error.message || 'Unknown error occurred';
            setError(key, errorMessage);
            setLocalError(errorMessage);
            throw error;
        } finally {
            setLoading(key, false);
            setLocalLoading(false);
        }
    }, [key, fetchFunction, setLoading, setError, clearError, updateCache]);
    
    // Auto-fetch on mount if dependencies provided
    useEffect(() => {
        if (dependencies.length > 0 && dependencies.every(dep => dep !== undefined)) {
            fetchData(...dependencies);
        }
    }, dependencies);
    
    return {
        data: cache[key],
        loading: loading[key] || localLoading,
        error: errors[key] || localError,
        refetch: fetchData,
        clearError: () => {
            clearError(key);
            setLocalError(null);
        }
    };
}

/**
 * Products API hook
 */
export function useProducts(filters = {}, autoFetch = true) {
    const { 
        cache, 
        loading, 
        errors, 
        pagination,
        setLoading, 
        setError, 
        setProducts,
        setFilters
    } = useApi();
    
    const fetchProducts = useCallback(async (newFilters = {}) => {
        try {
            setLoading('products', true);
            
            const mergedFilters = { ...filters, ...newFilters };
            setFilters('products', mergedFilters);
            
            const result = await apiAdapter.getProducts(mergedFilters);
            
            if (result.success) {
                setProducts(result.data, result.meta);
                return result;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            setError('products', error.message);
            throw error;
        }
    }, [filters, setLoading, setError, setProducts, setFilters]);
    
    // Auto-fetch on mount
    useEffect(() => {
        if (autoFetch) {
            fetchProducts();
        }
    }, [autoFetch]);
    
    return {
        products: cache.productsList,
        loading: loading.products,
        error: errors.products,
        pagination: pagination.products,
        fetchProducts,
        refetch: () => fetchProducts()
    };
}

/**
 * Single product API hook
 */
export function useProduct(productId, options = {}) {
    const { 
        cache, 
        loading, 
        errors, 
        setLoading, 
        setError, 
        setProduct 
    } = useApi();
    
    const fetchProduct = useCallback(async (id = productId, opts = options) => {
        if (!id) return;
        
        try {
            setLoading('products', true);
            
            const result = await apiAdapter.getProduct(id, opts);
            
            if (result.success) {
                setProduct(id, result.data);
                return result;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            setError('products', error.message);
            throw error;
        }
    }, [productId, options, setLoading, setError, setProduct]);
    
    // Auto-fetch on mount
    useEffect(() => {
        if (productId) {
            fetchProduct();
        }
    }, [productId]);
    
    return {
        product: cache.products[productId],
        loading: loading.products,
        error: errors.products,
        fetchProduct,
        refetch: () => fetchProduct()
    };
}

/**
 * Categories API hook
 */
export function useCategories(filters = {}, autoFetch = true) {
    const { 
        cache, 
        loading, 
        errors, 
        setLoading, 
        setError, 
        setCategories 
    } = useApi();
    
    const fetchCategories = useCallback(async (newFilters = {}) => {
        try {
            setLoading('categories', true);
            
            const mergedFilters = { ...filters, ...newFilters };
            const result = await apiAdapter.getCategories(mergedFilters);
            
            if (result.success) {
                setCategories(result.data, result.meta);
                return result;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            setError('categories', error.message);
            throw error;
        }
    }, [filters, setLoading, setError, setCategories]);
    
    // Auto-fetch on mount
    useEffect(() => {
        if (autoFetch) {
            fetchCategories();
        }
    }, [autoFetch]);
    
    return {
        categories: cache.categoriesList,
        loading: loading.categories,
        error: errors.categories,
        fetchCategories,
        refetch: () => fetchCategories()
    };
}

/**
 * Orders API hook
 */
export function useOrders(filters = {}, autoFetch = true) {
    const { 
        cache, 
        loading, 
        errors, 
        pagination,
        setLoading, 
        setError, 
        setOrders 
    } = useApi();
    
    const fetchOrders = useCallback(async (newFilters = {}) => {
        try {
            setLoading('orders', true);
            
            const mergedFilters = { ...filters, ...newFilters };
            const result = await apiAdapter.getOrders(mergedFilters);
            
            if (result.success) {
                setOrders(result.data, result.meta);
                return result;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            setError('orders', error.message);
            throw error;
        }
    }, [filters, setLoading, setError, setOrders]);
    
    // Auto-fetch on mount
    useEffect(() => {
        if (autoFetch) {
            fetchOrders();
        }
    }, [autoFetch]);
    
    return {
        orders: cache.ordersList,
        loading: loading.orders,
        error: errors.orders,
        pagination: pagination.orders,
        fetchOrders,
        refetch: () => fetchOrders()
    };
}

/**
 * Search hook
 */
export function useSearch() {
    const [searchResults, setSearchResults] = useState([]);
    const [searchLoading, setSearchLoading] = useState(false);
    const [searchError, setSearchError] = useState(null);
    
    const search = useCallback(async (searchTerm, filters = {}) => {
        if (!searchTerm.trim()) {
            setSearchResults([]);
            return;
        }
        
        try {
            setSearchLoading(true);
            setSearchError(null);
            
            const result = await apiAdapter.searchProducts(searchTerm, filters);
            
            if (result.success) {
                setSearchResults(result.data);
                return result;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            setSearchError(error.message);
            setSearchResults([]);
            throw error;
        } finally {
            setSearchLoading(false);
        }
    }, []);
    
    const clearSearch = useCallback(() => {
        setSearchResults([]);
        setSearchError(null);
    }, []);
    
    return {
        searchResults,
        searchLoading,
        searchError,
        search,
        clearSearch
    };
}

/**
 * Mutation hook for create/update/delete operations
 */
export function useMutation(mutationFunction) {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [data, setData] = useState(null);
    
    const mutate = useCallback(async (...args) => {
        try {
            setLoading(true);
            setError(null);
            
            const result = await mutationFunction(...args);
            
            if (result.success) {
                setData(result.data);
                return result;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            setError(error.message);
            throw error;
        } finally {
            setLoading(false);
        }
    }, [mutationFunction]);
    
    const reset = useCallback(() => {
        setLoading(false);
        setError(null);
        setData(null);
    }, []);
    
    return {
        mutate,
        loading,
        error,
        data,
        reset
    };
}
