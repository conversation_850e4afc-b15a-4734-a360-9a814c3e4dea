import { useState, useCallback, useRef } from 'react';

/**
 * Advanced Form Management Hook
 * Phase 4B: State Management Modernizasyonu
 */

export function useForm(initialValues = {}, options = {}) {
    const {
        validate,
        onSubmit,
        resetOnSubmit = false,
        validateOnChange = true,
        validateOnBlur = true
    } = options;
    
    const [values, setValues] = useState(initialValues);
    const [errors, setErrors] = useState({});
    const [touched, setTouched] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitCount, setSubmitCount] = useState(0);
    const [isValid, setIsValid] = useState(true);
    
    const initialValuesRef = useRef(initialValues);
    
    // Validation function
    const validateField = useCallback((name, value) => {
        if (!validate) return null;
        
        const fieldValidation = validate({ ...values, [name]: value });
        return fieldValidation[name] || null;
    }, [validate, values]);
    
    const validateForm = useCallback((formValues = values) => {
        if (!validate) return {};
        
        const validationErrors = validate(formValues);
        setIsValid(Object.keys(validationErrors).length === 0);
        return validationErrors;
    }, [validate, values]);
    
    // Field handlers
    const setValue = useCallback((name, value) => {
        setValues(prev => ({ ...prev, [name]: value }));
        
        if (validateOnChange) {
            const fieldError = validateField(name, value);
            setErrors(prev => ({
                ...prev,
                [name]: fieldError
            }));
        }
    }, [validateOnChange, validateField]);
    
    const setFieldValue = setValue; // Alias for consistency
    
    const setFieldError = useCallback((name, error) => {
        setErrors(prev => ({ ...prev, [name]: error }));
    }, []);
    
    const setFieldTouched = useCallback((name, isTouched = true) => {
        setTouched(prev => ({ ...prev, [name]: isTouched }));
    }, []);
    
    // Event handlers
    const handleChange = useCallback((event) => {
        const { name, value, type, checked } = event.target;
        const fieldValue = type === 'checkbox' ? checked : value;
        
        setValue(name, fieldValue);
    }, [setValue]);
    
    const handleBlur = useCallback((event) => {
        const { name } = event.target;
        setFieldTouched(name, true);
        
        if (validateOnBlur) {
            const fieldError = validateField(name, values[name]);
            setErrors(prev => ({
                ...prev,
                [name]: fieldError
            }));
        }
    }, [validateOnBlur, validateField, values, setFieldTouched]);
    
    // Form submission
    const handleSubmit = useCallback(async (event) => {
        if (event) {
            event.preventDefault();
        }
        
        setSubmitCount(prev => prev + 1);
        
        // Mark all fields as touched
        const allTouched = Object.keys(values).reduce((acc, key) => {
            acc[key] = true;
            return acc;
        }, {});
        setTouched(allTouched);
        
        // Validate form
        const validationErrors = validateForm();
        setErrors(validationErrors);
        
        if (Object.keys(validationErrors).length > 0) {
            return;
        }
        
        if (!onSubmit) {
            return;
        }
        
        try {
            setIsSubmitting(true);
            await onSubmit(values);
            
            if (resetOnSubmit) {
                resetForm();
            }
        } catch (error) {
            // Handle submission errors
            if (error.errors) {
                setErrors(error.errors);
            }
            throw error;
        } finally {
            setIsSubmitting(false);
        }
    }, [values, validateForm, onSubmit, resetOnSubmit]);
    
    // Form reset
    const resetForm = useCallback((newValues = initialValuesRef.current) => {
        setValues(newValues);
        setErrors({});
        setTouched({});
        setIsSubmitting(false);
        setSubmitCount(0);
        setIsValid(true);
    }, []);
    
    // Utility functions
    const getFieldProps = useCallback((name) => ({
        name,
        value: values[name] || '',
        onChange: handleChange,
        onBlur: handleBlur
    }), [values, handleChange, handleBlur]);
    
    const getFieldMeta = useCallback((name) => ({
        error: errors[name],
        touched: touched[name],
        invalid: !!(errors[name] && touched[name])
    }), [errors, touched]);
    
    const setValues = useCallback((newValues) => {
        if (typeof newValues === 'function') {
            setValues(prev => newValues(prev));
        } else {
            setValues(newValues);
        }
    }, []);
    
    const setErrors = useCallback((newErrors) => {
        if (typeof newErrors === 'function') {
            setErrors(prev => newErrors(prev));
        } else {
            setErrors(newErrors);
        }
    }, []);
    
    // Check if form is dirty (has changes)
    const isDirty = JSON.stringify(values) !== JSON.stringify(initialValuesRef.current);
    
    // Check if form has been touched
    const isTouched = Object.values(touched).some(Boolean);
    
    return {
        // Values
        values,
        errors,
        touched,
        
        // State
        isSubmitting,
        isValid,
        isDirty,
        isTouched,
        submitCount,
        
        // Setters
        setValues,
        setErrors,
        setValue,
        setFieldValue,
        setFieldError,
        setFieldTouched,
        
        // Handlers
        handleChange,
        handleBlur,
        handleSubmit,
        
        // Utilities
        getFieldProps,
        getFieldMeta,
        resetForm,
        validateForm
    };
}

/**
 * Validation helper functions
 */
export const validators = {
    required: (message = 'Bu alan zorunludur') => (value) => {
        if (!value || (typeof value === 'string' && !value.trim())) {
            return message;
        }
        return null;
    },
    
    email: (message = 'Geçerli bir email adresi giriniz') => (value) => {
        if (!value) return null;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value) ? null : message;
    },
    
    minLength: (min, message) => (value) => {
        if (!value) return null;
        const msg = message || `En az ${min} karakter olmalıdır`;
        return value.length >= min ? null : msg;
    },
    
    maxLength: (max, message) => (value) => {
        if (!value) return null;
        const msg = message || `En fazla ${max} karakter olmalıdır`;
        return value.length <= max ? null : msg;
    },
    
    min: (min, message) => (value) => {
        if (value === '' || value === null || value === undefined) return null;
        const msg = message || `En az ${min} olmalıdır`;
        return Number(value) >= min ? null : msg;
    },
    
    max: (max, message) => (value) => {
        if (value === '' || value === null || value === undefined) return null;
        const msg = message || `En fazla ${max} olmalıdır`;
        return Number(value) <= max ? null : msg;
    },
    
    pattern: (regex, message = 'Geçersiz format') => (value) => {
        if (!value) return null;
        return regex.test(value) ? null : message;
    },
    
    url: (message = 'Geçerli bir URL giriniz') => (value) => {
        if (!value) return null;
        try {
            new URL(value);
            return null;
        } catch {
            return message;
        }
    },
    
    phone: (message = 'Geçerli bir telefon numarası giriniz') => (value) => {
        if (!value) return null;
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(value.replace(/\s/g, '')) ? null : message;
    }
};

/**
 * Combine multiple validators
 */
export function combineValidators(...validators) {
    return (value) => {
        for (const validator of validators) {
            const error = validator(value);
            if (error) return error;
        }
        return null;
    };
}

/**
 * Create form validation schema
 */
export function createValidationSchema(schema) {
    return (values) => {
        const errors = {};
        
        Object.keys(schema).forEach(field => {
            const validator = schema[field];
            const value = values[field];
            
            if (typeof validator === 'function') {
                const error = validator(value);
                if (error) errors[field] = error;
            } else if (Array.isArray(validator)) {
                const combinedValidator = combineValidators(...validator);
                const error = combinedValidator(value);
                if (error) errors[field] = error;
            }
        });
        
        return errors;
    };
}
