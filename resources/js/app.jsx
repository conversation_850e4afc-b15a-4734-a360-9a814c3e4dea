import "./bootstrap";
import "../css/app.css";

import { createRoot } from "react-dom/client";
import { createInertiaApp } from "@inertiajs/react";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { Toaster } from "react-hot-toast";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// Phase 4B: State Management Modernizasyonu
import { ApiProvider } from "./Contexts/ApiContext.jsx";
import ErrorBoundary from "./Components/ui/ErrorBoundary.jsx";

const appName = import.meta.env.VITE_APP_NAME || "Laravel";

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) =>
        resolvePageComponent(
            `./Pages/${name}.jsx`,
            import.meta.glob("./Pages/**/*.jsx")
        ),
    setup({ el, App, props }) {
        const root = createRoot(el);

        root.render(
            <ErrorBoundary>
                <ApiProvider>
                    <App {...props} />
                    <Toaster position="bottom-right" />
                    <ToastContainer position="bottom-right" autoClose={3000} />
                </ApiProvider>
            </ErrorBoundary>
        );
    },
    progress: {
        color: "#4B5563",
    },
});
